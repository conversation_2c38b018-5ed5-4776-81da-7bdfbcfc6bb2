require('dotenv').config();

const readline = require('readline');
const { spawn, exec } = require('child_process');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');

// Enhanced ASM System with Intelligent AI
class EnhancedASMSystem {
    constructor() {
        this.setupDirectories();
        this.initializeAI();
        this.setupInterface();
        this.chatHistory = [];
        this.sessionTarget = '';
        this.scanResults = {};
        this.activeScans = new Map();
        this.riskAssessment = { level: 'UNKNOWN', score: 0, findings: [] };
        this.isInitialized = false;
    }

    setupDirectories() {
        const dirs = ['scan_reports', 'logs', 'temp', 'assets'];
        dirs.forEach(dir => {
            const dirPath = path.join(__dirname, dir);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath);
                console.log(`[+] Created directory: ${dirPath}`);
            }
        });
    }

    initializeAI() {
        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            console.error('[ERROR] GEMINI_API_KEY environment variable not set.');
            process.exit(1);
        }
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    }

    setupInterface() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: '🔍 ASM-AI> '
        });
    }

    // Enhanced AI System Prompt with Intelligence
    getEnhancedSystemPrompt() {
        return `You are an Advanced AI Attack Surface Management (ASM) System with the following capabilities:

**CORE INTELLIGENCE:**
- Autonomous decision making for reconnaissance strategies
- Real-time risk assessment and prioritization
- Intelligent correlation of findings across multiple tools
- Adaptive scanning based on discovered assets
- Continuous learning from scan results

**ENHANCED CAPABILITIES:**
1. **Smart Reconnaissance:** Automatically determine the best tool sequence based on target type
2. **Risk-Based Prioritization:** Focus on high-risk findings first
3. **Intelligent Correlation:** Connect findings across different scan types
4. **Adaptive Scanning:** Modify approach based on discovered assets
5. **Real-time Analysis:** Provide insights as data comes in

**OPERATIONAL MODES:**
- PASSIVE: Subdomain enumeration, DNS analysis, OSINT
- ACTIVE: Port scanning, service detection, vulnerability assessment
- CONTINUOUS: Ongoing monitoring and change detection
- EMERGENCY: Rapid assessment for incident response

**DECISION FRAMEWORK:**
For each target, you should:
1. Analyze target type (domain, IP, organization)
2. Determine optimal reconnaissance strategy
3. Execute tools in logical sequence
4. Correlate findings in real-time
5. Assess risk levels continuously
6. Provide actionable intelligence

**RESPONSE FORMAT:**
Always structure responses with:
- ANALYSIS: What you discovered
- RISK_LEVEL: LOW/MEDIUM/HIGH/CRITICAL
- NEXT_ACTION: Recommended next step
- COMMAND: Single bash command in code block (if applicable)

You must think strategically and provide intelligent analysis, not just execute commands.`;
    }

    initializeSession(target) {
        this.sessionTarget = target.replace(/[^a-zA-Z0-9.-]/g, '_');
        this.chatHistory = [
            { role: 'user', parts: [{ text: this.getEnhancedSystemPrompt() }] },
            { role: 'model', parts: [{ text: `🤖 Enhanced ASM System Initialized for target: ${target}\n\nI'm analyzing the target type and preparing an intelligent reconnaissance strategy. Ready to begin comprehensive assessment.` }] }
        ];
        this.isInitialized = true;
        console.log(`\n🎯 [ENHANCED ASM] Session initialized for: ${target}`);
        console.log(`📊 [INTELLIGENCE] Preparing adaptive scanning strategy...`);
    }

    // Enhanced command parsing with intelligence
    parseAIResponse(text) {
        const analysis = this.extractSection(text, 'ANALYSIS');
        const riskLevel = this.extractSection(text, 'RISK_LEVEL');
        const nextAction = this.extractSection(text, 'NEXT_ACTION');
        const command = this.parseCommand(text);

        return {
            analysis: analysis || 'No analysis provided',
            riskLevel: riskLevel || 'UNKNOWN',
            nextAction: nextAction || 'Continue assessment',
            command: command
        };
    }

    extractSection(text, sectionName) {
        const regex = new RegExp(`${sectionName}:\\s*([^\\n]*(?:\\n(?!\\w+:)[^\\n]*)*)`, 'i');
        const match = text.match(regex);
        return match ? match[1].trim() : null;
    }

    parseCommand(text) {
        const match = text.match(/```bash\n([\s\S]*?)\n```/);
        return match ? match[1].trim() : null;
    }

    // Intelligent scan management
    async executeIntelligentScan(command, callback) {
        console.log(`\n🚀 [EXECUTING] ${command}`);
        
        // Track active scans
        const scanId = Date.now().toString();
        this.activeScans.set(scanId, {
            command: command,
            startTime: new Date(),
            status: 'running'
        });

        let stdout = '';
        let stderr = '';

        const parts = command.split(' ');
        const tool = parts[0];
        const args = parts.slice(1);

        const child = spawn(tool, args);

        child.stdout.on('data', (data) => {
            const output = data.toString();
            process.stdout.write(output);
            stdout += output;
            
            // Real-time analysis of output
            this.analyzeRealTimeOutput(tool, output);
        });

        child.stderr.on('data', (data) => {
            const output = data.toString();
            process.stderr.write(output);
            stderr += output;
        });

        child.on('close', (code) => {
            console.log(`\n✅ [COMPLETED] Command finished with code ${code}`);
            
            // Update scan status
            const scan = this.activeScans.get(scanId);
            if (scan) {
                scan.status = code === 0 ? 'completed' : 'failed';
                scan.endTime = new Date();
                scan.duration = scan.endTime - scan.startTime;
            }

            // Store results for correlation
            this.storeResults(tool, { stdout, stderr, code });
            
            callback(null, { code, stdout, stderr });
        });

        child.on('error', (error) => {
            console.error(`\n❌ [ERROR] ${error.message}`);
            this.activeScans.get(scanId).status = 'error';
            callback(error);
        });
    }

    // Real-time output analysis
    analyzeRealTimeOutput(tool, output) {
        // Intelligent parsing based on tool type
        switch (tool) {
            case 'subfinder':
                this.parseSubdomains(output);
                break;
            case 'nmap':
                this.parsePortScan(output);
                break;
            case 'nuclei':
                this.parseVulnerabilities(output);
                break;
        }
    }

    parseSubdomains(output) {
        const subdomains = output.match(/[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g);
        if (subdomains) {
            if (!this.scanResults.subdomains) this.scanResults.subdomains = new Set();
            subdomains.forEach(sub => this.scanResults.subdomains.add(sub));
            console.log(`📡 [DISCOVERY] Found ${this.scanResults.subdomains.size} unique subdomains`);
        }
    }

    parsePortScan(output) {
        const openPorts = output.match(/(\d+)\/tcp\s+open/g);
        if (openPorts) {
            if (!this.scanResults.openPorts) this.scanResults.openPorts = [];
            openPorts.forEach(port => {
                const portNum = port.match(/(\d+)/)[1];
                if (!this.scanResults.openPorts.includes(portNum)) {
                    this.scanResults.openPorts.push(portNum);
                    console.log(`🔓 [PORT] Discovered open port: ${portNum}`);
                }
            });
        }
    }

    parseVulnerabilities(output) {
        if (output.includes('[CRITICAL]') || output.includes('[HIGH]')) {
            this.riskAssessment.level = 'HIGH';
            this.riskAssessment.score += 10;
            console.log(`🚨 [HIGH RISK] Critical vulnerability detected!`);
        } else if (output.includes('[MEDIUM]')) {
            this.riskAssessment.score += 5;
            console.log(`⚠️  [MEDIUM RISK] Vulnerability found`);
        }
    }

    storeResults(tool, results) {
        const timestamp = new Date().toISOString();
        const filename = `${timestamp}_${this.sessionTarget}_${tool}.log`;
        const filepath = path.join(__dirname, 'scan_reports', filename);
        
        const logContent = `Command: ${tool}
Exit Code: ${results.code}

--- STDOUT ---
${results.stdout}

--- STDERR ---
${results.stderr}
`;
        
        fs.writeFileSync(filepath, logContent);
        console.log(`💾 [STORED] Results saved to: ${filename}`);
    }

    // Enhanced chat loop with intelligence
    async enhancedChatLoop(userInput) {
        try {
            this.chatHistory.push({ role: 'user', parts: [{ text: userInput }] });
            const chat = this.model.startChat({ history: this.chatHistory });
            const result = await chat.sendMessage(userInput);
            const response = await result.response;
            const aiText = response.text();

            this.chatHistory.push({ role: 'model', parts: [{ text: aiText }] });

            // Parse AI response with intelligence
            const parsedResponse = this.parseAIResponse(aiText);
            
            console.log(`\n🤖 [AI ANALYSIS] ${parsedResponse.analysis}`);
            console.log(`📊 [RISK LEVEL] ${parsedResponse.riskLevel}`);
            console.log(`🎯 [NEXT ACTION] ${parsedResponse.nextAction}`);

            if (parsedResponse.command) {
                this.rl.question(`\n🔍 Execute command: "${parsedResponse.command}"? (y/n/auto) `, (answer) => {
                    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'auto') {
                        if (parsedResponse.command.endsWith(' &')) {
                            console.log(`🚀 [BACKGROUND] Command launched: ${parsedResponse.command.trim()}`);
                            const feedback = `Command "${parsedResponse.command.trim()}" launched in background. Provide next intelligent action.`;
                            this.enhancedChatLoop(feedback);
                        } else {
                            this.executeIntelligentScan(parsedResponse.command, (err, result) => {
                                if (err) {
                                    const feedback = `❌ Execution Error: ${err.message}. Suggest alternative approach.`;
                                    this.enhancedChatLoop(feedback);
                                    return;
                                }
                                const feedback = `✅ Command completed. Results:\nExit Code: ${result.code}\nKey findings: ${this.summarizeResults(result)}\n\nProvide intelligent analysis and next action.`;
                                this.enhancedChatLoop(feedback);
                            });
                        }
                    } else {
                        console.log('❌ Execution cancelled.');
                        this.rl.prompt();
                    }
                });
            } else {
                this.rl.prompt();
            }
        } catch (error) {
            console.error(`\n❌ [AI ERROR] ${error.message}`);
            this.rl.prompt();
        }
    }

    summarizeResults(result) {
        // Intelligent result summarization
        const summary = [];
        if (this.scanResults.subdomains) {
            summary.push(`${this.scanResults.subdomains.size} subdomains`);
        }
        if (this.scanResults.openPorts) {
            summary.push(`${this.scanResults.openPorts.length} open ports`);
        }
        summary.push(`Risk: ${this.riskAssessment.level}`);
        return summary.join(', ') || 'Processing...';
    }

    // Start the enhanced system
    start() {
        console.log(`
🤖 ===============================================
   ENHANCED AI ATTACK SURFACE MANAGEMENT SYSTEM
   Powered by Google Gemini AI
===============================================

🎯 Features:
   • Intelligent reconnaissance strategies
   • Real-time risk assessment
   • Adaptive scanning capabilities
   • Continuous monitoring
   • Advanced correlation analysis

💡 Usage:
   1. Enter target domain/IP to begin
   2. AI will create intelligent scan strategy
   3. Real-time analysis and risk assessment
   4. Type 'exit' to quit

🔍 Enter target to begin assessment:`);

        this.rl.on('line', async (line) => {
            const trimmedLine = line.trim();
            if (trimmedLine.toLowerCase() === 'exit') {
                this.rl.close();
            } else if (!this.isInitialized) {
                this.initializeSession(trimmedLine);
                console.log(`\n🚀 What type of assessment would you like to perform?`);
                console.log(`   • 'passive' - OSINT and subdomain enumeration`);
                console.log(`   • 'active' - Full port scanning and vulnerability assessment`);
                console.log(`   • 'continuous' - Ongoing monitoring setup`);
                console.log(`   • 'auto' - Let AI decide the best approach`);
                this.rl.prompt();
            } else {
                await this.enhancedChatLoop(trimmedLine);
            }
        }).on('close', () => {
            console.log('\n👋 Enhanced ASM session closed. Goodbye!');
            process.exit(0);
        });

        this.rl.prompt();
    }
}

// Start the Enhanced ASM System
const asmSystem = new EnhancedASMSystem();
asmSystem.start();
