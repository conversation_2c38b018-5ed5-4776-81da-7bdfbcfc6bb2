const readline = require('readline');
const { exec } = require('child_process');
// You would also import your Gemini API call logic here

// Create an interface for reading from the terminal
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: 'gemini-black> '
});

console.log('Welcome to Gemini-Black Interactive. Type "exit" to quit.');
rl.prompt();

// This function simulates calling the Gemini API
async function getAIResponse(userInput) {
  // In a real app, you'd call the Gemini API here
  console.log('[Simulating AI thinking...]');
  // Let's pretend the AI responded with this text:
  return "Of course. Based on your request to scan, I recommend running this command:\n\n```bash\nwhois example.com\n```";
}

// Function to find a command within the AI's text
function parseCommand(text) {
  const match = text.match(/```bash\n([\s\S]*?)\n```/);
  return match ? match[1].trim() : null;
}

// Listen for each line of user input
rl.on('line', async (line) => {
  const userInput = line.trim();
  if (userInput === 'exit') {
    rl.close();
    return;
  }

  // 1. Get the AI's response
  const aiResponse = await getAIResponse(userInput);
  console.log(`🤖 AI: ${aiResponse}`);

  // 2. Parse the command from the response
  const commandToExecute = parseCommand(aiResponse);

  if (commandToExecute) {
    // 3. THE CRITICAL APPROVAL STEP
    rl.question(`Execute the command: "${commandToExecute}"? (y/n) `, (answer) => {
      if (answer.toLowerCase() === 'y') {
        // 4. Execute the command securely
        console.log(`[Executing...]`);
        exec(commandToExecute, (error, stdout, stderr) => {
          if (error) {
            console.error(`\n[Execution Error]:\n${error.message}`);
          }
          if (stderr) {
            console.error(`\n[Standard Error]:\n${stderr}`);
          }
          console.log(`\n[Output]:\n${stdout}`);
          rl.prompt(); // Show the prompt again for the next command
        });
      } else {
        console.log('Execution cancelled.');
        rl.prompt();
      }
    });
  } else {
    rl.prompt();
  }
}).on('close', () => {
  console.log('Exiting session. Goodbye!');
  process.exit(0);
});