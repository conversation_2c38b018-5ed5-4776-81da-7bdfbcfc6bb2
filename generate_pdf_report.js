const fs = require('fs');
const htmlPdf = require('html-pdf-node');

// Define paths
const reportLogPath = '/home/<USER>/gemini-black/scan_reports/2025-07-11T16-47-47.283Z_tngdigital.com.my_echo____Attack_Surface_Management_Report__tngdigit.log';
const outputPath = '/home/<USER>/gemini-black/scan_reports/enhanced_asm_report.pdf';

async function generatePdfReport() {
    try {
        // Read the content of the scan report log file
        const logContent = fs.readFileSync(reportLogPath, 'utf8');

        // Parse the log content to extract relevant sections
        const parseReport = (content) => {
            const sections = {};
            const lines = content.split('\n');
            let currentSection = '';
            let currentContent = [];

            for (const line of lines) {
                if (line.startsWith('## ')) {
                    if (currentSection) {
                        sections[currentSection] = currentContent.join('\n').trim();
                    }
                    currentSection = line.substring(3).trim().toLowerCase().replace(/ /g, '_');
                    currentContent = [];
                } else if (currentSection) {
                    currentContent.push(line);
                }
            }
            if (currentSection) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            return sections;
        };

        const parsedSections = parseReport(logContent);

        // Extract report title and target domain
        const firstLineMatch = logContent.match(/^#\s*(.*?:\s*)(.*)/);
        const reportTitle = firstLineMatch ? firstLineMatch[0].trim() : 'ASM Report';
        const targetDomain = firstLineMatch ? firstLineMatch[2].trim() : 'unknown.com';

        // Calculate metrics
        const subdomainCount = (parsedSections.subdomains_discovered || '').split('\n').filter(line => line.trim().startsWith('*')).length;
        const vulnerabilityCount = (parsedSections.potential_vulnerabilities || '').split(/^\d+\./).length - 1;
        const openPortsCount = (parsedSections.open_ports || '').split('**Port').length - 1;
        
        // Risk assessment
        const riskLevel = vulnerabilityCount > 3 ? 'HIGH' : vulnerabilityCount > 1 ? 'MEDIUM' : 'LOW';
        const riskColor = riskLevel === 'HIGH' ? '#dc3545' : riskLevel === 'MEDIUM' ? '#fd7e14' : '#28a745';

        // Generate HTML content for PDF
        const htmlContent = generatePdfHtmlTemplate({
            reportTitle,
            targetDomain,
            date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
            time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
            subdomainCount,
            vulnerabilityCount,
            openPortsCount,
            riskLevel,
            riskColor,
            executiveSummary: parsedSections.executive_summary || 'No executive summary available.',
            subdomainsDiscovered: parsedSections.subdomains_discovered || 'No subdomains discovered.',
            openPorts: parsedSections.open_ports || 'No open ports identified.',
            potentialVulnerabilities: parsedSections.potential_vulnerabilities || 'No vulnerabilities identified.',
            recommendations: parsedSections.recommendations || 'No specific recommendations available.'
        });

        // PDF options
        const options = {
            format: 'A4',
            margin: {
                top: '20mm',
                bottom: '20mm',
                left: '15mm',
                right: '15mm'
            },
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: '<div style="font-size:10px; text-align:center; width:100%;">ASM Report - Confidential</div>',
            footerTemplate: '<div style="font-size:10px; text-align:center; width:100%;"><span class="pageNumber"></span> of <span class="totalPages"></span></div>'
        };

        // Generate PDF
        const file = { content: htmlContent };
        const pdfBuffer = await htmlPdf.generatePdf(file, options);
        
        // Save the PDF file
        fs.writeFileSync(outputPath, pdfBuffer);
        console.log(`[+] Enhanced PDF report generated successfully at: ${outputPath}`);

    } catch (error) {
        console.error(`[-] Error generating PDF report: ${error.message}`);
    }
}

function generatePdfHtmlTemplate(data) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.reportTitle}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            font-size: 12px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .meta-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .meta-card {
            background: #f8f9fa;
            padding: 15px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .meta-card h3 {
            color: #666;
            font-size: 10px;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        
        .meta-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .risk-level {
            color: ${data.riskColor} !important;
        }
        
        .section {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 10px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 16px;
        }
        
        .section-content {
            padding: 15px;
            border: 1px solid #dee2e6;
            border-top: none;
        }
        
        .vulnerability-item {
            background: #fff5f5;
            border-left: 3px solid #dc3545;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .port-item {
            background: #f0f8ff;
            border-left: 3px solid #007bff;
            padding: 8px;
            margin-bottom: 8px;
        }
        
        .subdomain-list {
            columns: 2;
            column-gap: 15px;
            font-size: 10px;
        }
        
        .subdomain-item {
            break-inside: avoid;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        
        .recommendation {
            background: #f8f9fa;
            border-left: 3px solid #28a745;
            padding: 10px;
            margin-bottom: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TECHLAB SECURITY</h1>
        <div class="subtitle">CYBER THREAT INTELLIGENCE</div>
        <h2 style="margin-top: 15px;">ASM Report</h2>
        <div class="subtitle">${data.targetDomain}</div>
        <div style="margin-top: 8px; font-size: 12px;">Date: ${data.date} ${data.time}</div>
    </div>
    
    <div class="meta-info">
        <div class="meta-card">
            <h3>Subdomains Found</h3>
            <div class="value">${data.subdomainCount}</div>
        </div>
        <div class="meta-card">
            <h3>Open Ports</h3>
            <div class="value">${data.openPortsCount}</div>
        </div>
        <div class="meta-card">
            <h3>Vulnerabilities</h3>
            <div class="value">${data.vulnerabilityCount}</div>
        </div>
        <div class="meta-card">
            <h3>Risk Level</h3>
            <div class="value risk-level">${data.riskLevel}</div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-header">
            <h2>Executive Summary</h2>
        </div>
        <div class="section-content">
            <p>${data.executiveSummary.replace(/\n/g, '</p><p>')}</p>
        </div>
    </div>
    
    <div class="section page-break">
        <div class="section-header">
            <h2>Subdomains Discovered</h2>
        </div>
        <div class="section-content">
            <div class="subdomain-list">
                ${data.subdomainsDiscovered.split('\n')
                    .filter(line => line.trim().startsWith('*'))
                    .map(line => `<div class="subdomain-item">${line.trim()}</div>`)
                    .join('')}
            </div>
        </div>
    </div>
    
    <div class="section page-break">
        <div class="section-header">
            <h2>Open Ports</h2>
        </div>
        <div class="section-content">
            ${data.openPorts.split('\n')
                .filter(line => line.includes('**Port'))
                .map(line => `<div class="port-item">${line.replace(/\*\*/g, '')}</div>`)
                .join('')}
        </div>
    </div>
    
    <div class="section page-break">
        <div class="section-header">
            <h2>Potential Vulnerabilities</h2>
        </div>
        <div class="section-content">
            ${data.potentialVulnerabilities.split(/^\d+\./).slice(1)
                .map(vuln => `<div class="vulnerability-item">${vuln.trim()}</div>`)
                .join('')}
        </div>
    </div>
    
    <div class="section page-break">
        <div class="section-header">
            <h2>Recommendations</h2>
        </div>
        <div class="section-content">
            ${data.recommendations.split(/^\d+\./).slice(1)
                .map(rec => `<div class="recommendation">${rec.trim()}</div>`)
                .join('')}
        </div>
    </div>
</body>
</html>`;
}

generatePdfReport();
