const fs = require('fs');
const path = require('path');

// Define paths
const reportLogPath = '/home/<USER>/gemini-black/scan_reports/2025-07-11T16-47-47.283Z_tngdigital.com.my_echo____Attack_Surface_Management_Report__tngdigit.log';
const outputPath = '/home/<USER>/gemini-black/scan_reports/enhanced_asm_report.html';

async function generateHtmlReport() {
    try {
        // Read the content of the scan report log file
        const logContent = fs.readFileSync(reportLogPath, 'utf8');

        // Parse the log content to extract relevant sections
        const parseReport = (content) => {
            const sections = {};
            const lines = content.split('\n');
            let currentSection = '';
            let currentContent = [];

            for (const line of lines) {
                if (line.startsWith('## ')) {
                    if (currentSection) {
                        sections[currentSection] = currentContent.join('\n').trim();
                    }
                    currentSection = line.substring(3).trim().toLowerCase().replace(/ /g, '_');
                    currentContent = [];
                } else if (currentSection) {
                    currentContent.push(line);
                }
            }
            if (currentSection) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            return sections;
        };

        const parsedSections = parseReport(logContent);

        // Extract report title and target domain
        const firstLineMatch = logContent.match(/^#\s*(.*?:\s*)(.*)/);
        const reportTitle = firstLineMatch ? firstLineMatch[0].trim() : 'ASM Report';
        const targetDomain = firstLineMatch ? firstLineMatch[2].trim() : 'unknown.com';

        // Calculate metrics
        const subdomainCount = (parsedSections.subdomains_discovered || '').split('\n').filter(line => line.trim().startsWith('*')).length;
        const vulnerabilityCount = (parsedSections.potential_vulnerabilities || '').split(/^\d+\./).length - 1;
        const openPortsCount = (parsedSections.open_ports || '').split('**Port').length - 1;
        
        // Risk assessment
        const riskLevel = vulnerabilityCount > 3 ? 'HIGH' : vulnerabilityCount > 1 ? 'MEDIUM' : 'LOW';
        const riskColor = riskLevel === 'HIGH' ? '#dc3545' : riskLevel === 'MEDIUM' ? '#fd7e14' : '#28a745';

        // Generate HTML content
        const htmlContent = generateHtmlTemplate({
            reportTitle,
            targetDomain,
            date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
            time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
            subdomainCount,
            vulnerabilityCount,
            openPortsCount,
            riskLevel,
            riskColor,
            executiveSummary: parsedSections.executive_summary || 'No executive summary available.',
            subdomainsDiscovered: parsedSections.subdomains_discovered || 'No subdomains discovered.',
            openPorts: parsedSections.open_ports || 'No open ports identified.',
            potentialVulnerabilities: parsedSections.potential_vulnerabilities || 'No vulnerabilities identified.',
            recommendations: parsedSections.recommendations || 'No specific recommendations available.'
        });

        // Save the HTML file
        fs.writeFileSync(outputPath, htmlContent);
        console.log(`[+] Enhanced HTML report generated successfully at: ${outputPath}`);

    } catch (error) {
        console.error(`[-] Error generating HTML report: ${error.message}`);
    }
}

function generateHtmlTemplate(data) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.reportTitle}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .meta-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .meta-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .meta-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            margin-bottom: 10px;
        }
        
        .meta-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        
        .risk-level {
            color: ${data.riskColor} !important;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .vulnerability-item {
            background: #fff5f5;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 4px 4px 0;
        }
        
        .port-item {
            background: #f0f8ff;
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 0 4px 4px 0;
        }
        
        .subdomain-list {
            columns: 2;
            column-gap: 20px;
        }
        
        .subdomain-item {
            break-inside: avoid;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .recommendation {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 4px 4px 0;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        @media print {
            body { background: white; }
            .container { max-width: none; }
            .section { box-shadow: none; border: 1px solid #ddd; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TECHLAB SECURITY</h1>
            <div class="subtitle">CYBER THREAT INTELLIGENCE</div>
            <h2 style="margin-top: 20px;">ASM Report</h2>
            <div class="subtitle">${data.targetDomain}</div>
            <div style="margin-top: 10px; font-size: 0.9em;">Date: ${data.date} ${data.time}</div>
        </div>
        
        <div class="meta-info">
            <div class="meta-card">
                <h3>Subdomains Found</h3>
                <div class="value">${data.subdomainCount}</div>
            </div>
            <div class="meta-card">
                <h3>Open Ports</h3>
                <div class="value">${data.openPortsCount}</div>
            </div>
            <div class="meta-card">
                <h3>Vulnerabilities</h3>
                <div class="value">${data.vulnerabilityCount}</div>
            </div>
            <div class="meta-card">
                <h3>Risk Level</h3>
                <div class="value risk-level">${data.riskLevel}</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>Executive Summary</h2>
            </div>
            <div class="section-content">
                <p>${data.executiveSummary.replace(/\n/g, '</p><p>')}</p>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>Subdomains Discovered</h2>
            </div>
            <div class="section-content">
                <div class="subdomain-list">
                    ${data.subdomainsDiscovered.split('\n')
                        .filter(line => line.trim().startsWith('*'))
                        .map(line => `<div class="subdomain-item">${line.trim()}</div>`)
                        .join('')}
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>Open Ports</h2>
            </div>
            <div class="section-content">
                ${data.openPorts.split('\n')
                    .filter(line => line.includes('**Port'))
                    .map(line => `<div class="port-item">${line.replace(/\*\*/g, '')}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>Potential Vulnerabilities</h2>
            </div>
            <div class="section-content">
                ${data.potentialVulnerabilities.split(/^\d+\./).slice(1)
                    .map(vuln => `<div class="vulnerability-item">${vuln.trim()}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">
                <h2>Recommendations</h2>
            </div>
            <div class="section-content">
                ${data.recommendations.split(/^\d+\./).slice(1)
                    .map(rec => `<div class="recommendation">${rec.trim()}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="footer">
            <p>This report was generated automatically by the ASM Scanner v2.0</p>
            <p>© 2025 TechLab Security - Confidential</p>
        </div>
    </div>
</body>
</html>`;
}

generateHtmlReport();
