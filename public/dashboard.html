<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive ASM Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: #0a0a0a; color: #fff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; }
        .header h1 { font-size: 2em; margin-bottom: 10px; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; padding: 20px; height: calc(100vh - 120px); }
        .panel { background: #1a1a1a; border-radius: 10px; padding: 20px; overflow: hidden; }
        .panel h2 { color: #667eea; margin-bottom: 15px; border-bottom: 2px solid #333; padding-bottom: 10px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .metric { background: #2a2a2a; padding: 15px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .metric-label { font-size: 0.9em; color: #ccc; margin-top: 5px; }
        .chat-container { display: flex; flex-direction: column; height: 100%; }
        .chat-messages { flex: 1; overflow-y: auto; padding: 10px; background: #111; border-radius: 5px; margin-bottom: 15px; }
        .message { margin-bottom: 15px; padding: 10px; border-radius: 8px; }
        .message.user { background: #2a4a7a; margin-left: 20px; }
        .message.ai { background: #2a7a4a; margin-right: 20px; }
        .message.system { background: #7a4a2a; font-style: italic; }
        .input-group { display: flex; gap: 10px; }
        .input-group input { flex: 1; padding: 10px; border: none; border-radius: 5px; background: #333; color: #fff; }
        .input-group button { padding: 10px 20px; border: none; border-radius: 5px; background: #667eea; color: #fff; cursor: pointer; }
        .input-group button:hover { background: #5a6fd8; }
        .terminal { background: #000; color: #0f0; font-family: 'Courier New', monospace; padding: 15px; border-radius: 5px; height: 300px; overflow-y: auto; }
        .risk-high { color: #ff4444; }
        .risk-medium { color: #ffaa44; }
        .risk-low { color: #44ff44; }
        .findings-list { max-height: 200px; overflow-y: auto; }
        .finding-item { background: #2a2a2a; margin: 5px 0; padding: 8px; border-radius: 4px; font-size: 0.9em; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #ffaa44; }
        .status-completed { background: #44ff44; }
        .status-error { background: #ff4444; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Interactive ASM Dashboard</h1>
        <p>AI-Powered Attack Surface Management System</p>
    </div>

    <div class="container">
        <div class="panel">
            <h2>📊 Real-time Metrics</h2>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="subdomains-count">0</div>
                    <div class="metric-label">Subdomains</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="ports-count">0</div>
                    <div class="metric-label">Open Ports</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="vulns-count">0</div>
                    <div class="metric-label">Vulnerabilities</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="risk-level">UNKNOWN</div>
                    <div class="metric-label">Risk Level</div>
                </div>
            </div>

            <h3>🎯 Current Target</h3>
            <div id="current-target" style="background: #2a2a2a; padding: 10px; border-radius: 5px; margin: 10px 0;">
                No active session
            </div>

            <h3>🔍 Recent Findings</h3>
            <div id="findings-list" class="findings-list">
                <div class="finding-item">Start a new scan to see findings...</div>
            </div>
        </div>

        <div class="panel">
            <h2>💬 AI Assistant</h2>
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <div class="message system">
                        🤖 AI Assistant ready. Start a new scan or ask questions about ASM techniques.
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="message-input" placeholder="Enter target domain or ask AI assistant..." />
                    <button onclick="sendMessage()">Send</button>
                    <button onclick="startNewScan()">New Scan</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <h2>💻 Live Terminal Output</h2>
            <div id="terminal" class="terminal">
                Welcome to Interactive ASM System\n
                Ready to execute commands...\n
            </div>
        </div>

        <div class="panel">
            <h2>📈 Session Status</h2>
            <div id="session-status">
                <div style="background: #2a2a2a; padding: 15px; border-radius: 5px;">
                    <span class="status-indicator status-completed"></span>
                    System Ready - No active scans
                </div>
            </div>

            <h3 style="margin-top: 20px;">🔧 Quick Actions</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                <button onclick="startPassiveScan()" style="padding: 10px; background: #2a7a4a; border: none; border-radius: 5px; color: white; cursor: pointer;">Passive Scan</button>
                <button onclick="startActiveScan()" style="padding: 10px; background: #7a4a2a; border: none; border-radius: 5px; color: white; cursor: pointer;">Active Scan</button>
                <button onclick="generateReport()" style="padding: 10px; background: #4a2a7a; border: none; border-radius: 5px; color: white; cursor: pointer;">Generate Report</button>
                <button onclick="clearSession()" style="padding: 10px; background: #7a2a2a; border: none; border-radius: 5px; color: white; cursor: pointer;">Clear Session</button>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        let currentSessionId = null;

        // Socket event handlers
        socket.on('session-update', (session) => {
            updateDashboard(session);
        });

        socket.on('command-output', (data) => {
            appendToTerminal(data.data);
        });

        socket.on('command-complete', (data) => {
            appendToTerminal(`\n✅ Command completed: ${data.command}\n`);
        });

        // Dashboard functions
        function updateDashboard(session) {
            currentSessionId = session.id;

            // Update metrics
            document.getElementById('subdomains-count').textContent = session.findings.subdomains.length;
            document.getElementById('ports-count').textContent = session.findings.openPorts.length;
            document.getElementById('vulns-count').textContent = session.findings.vulnerabilities.length;

            const riskElement = document.getElementById('risk-level');
            riskElement.textContent = session.findings.riskLevel;
            riskElement.className = `metric-value risk-${session.findings.riskLevel.toLowerCase()}`;

            // Update target
            document.getElementById('current-target').textContent = session.target;

            // Update chat
            updateChat(session.chatHistory);

            // Update findings
            updateFindings(session.findings);
        }

        function updateChat(chatHistory) {
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.innerHTML = '';

            chatHistory.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.type}`;
                messageDiv.innerHTML = `
                    <strong>${msg.type === 'user' ? '👤 You' : '🤖 AI'}:</strong>
                    <div>${msg.message}</div>
                    ${msg.command ? `<div style="background: #333; padding: 5px; margin-top: 5px; border-radius: 3px; font-family: monospace;">💻 ${msg.command}</div>` : ''}
                `;
                chatMessages.appendChild(messageDiv);
            });

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function updateFindings(findings) {
            const findingsList = document.getElementById('findings-list');
            findingsList.innerHTML = '';

            findings.subdomains.slice(-5).forEach(sub => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.textContent = `🌐 Subdomain: ${sub}`;
                findingsList.appendChild(item);
            });

            findings.openPorts.slice(-5).forEach(port => {
                const item = document.createElement('div');
                item.className = 'finding-item';
                item.textContent = `🔓 Open Port: ${port}`;
                findingsList.appendChild(item);
            });
        }

        function appendToTerminal(text) {
            const terminal = document.getElementById('terminal');
            terminal.textContent += text;
            terminal.scrollTop = terminal.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (!message) return;

            if (currentSessionId) {
                socket.emit('send-message', {
                    sessionId: currentSessionId,
                    message: message
                });
            } else {
                // Start new session
                startNewScan(message);
            }

            input.value = '';
        }

        async function startNewScan(target) {
            if (!target) {
                target = prompt('Enter target domain or IP:');
            }

            if (!target) return;

            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: 'auto' })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;

                socket.emit('join-session', currentSessionId);

                appendToTerminal(`🚀 Started new scan session: ${currentSessionId}\n`);
                appendToTerminal(`🎯 Target: ${target}\n`);

            } catch (error) {
                console.error('Error starting scan:', error);
                appendToTerminal(`❌ Error starting scan: ${error.message}\n`);
            }
        }

        function startPassiveScan() {
            const target = prompt('Enter target for passive scan:');
            if (target) {
                startScanWithType(target, 'passive');
            }
        }

        function startActiveScan() {
            const target = prompt('Enter target for active scan:');
            if (target) {
                startScanWithType(target, 'active');
            }
        }

        async function startScanWithType(target, scanType) {
            try {
                const response = await fetch('/api/scan/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: target, scanType: scanType })
                });

                const result = await response.json();
                currentSessionId = result.sessionId;
                socket.emit('join-session', currentSessionId);

            } catch (error) {
                console.error('Error starting scan:', error);
            }
        }

        function generateReport() {
            if (currentSessionId) {
                window.open(`/api/scan/${currentSessionId}/report`, '_blank');
            } else {
                alert('No active session to generate report for');
            }
        }

        function clearSession() {
            currentSessionId = null;
            document.getElementById('chat-messages').innerHTML = '<div class="message system">🤖 Session cleared. Ready for new scan.</div>';
            document.getElementById('terminal').textContent = 'Session cleared...\n';

            // Reset metrics
            document.getElementById('subdomains-count').textContent = '0';
            document.getElementById('ports-count').textContent = '0';
            document.getElementById('vulns-count').textContent = '0';
            document.getElementById('risk-level').textContent = 'UNKNOWN';
            document.getElementById('current-target').textContent = 'No active session';
        }

        // Enter key support
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initialize dashboard
        appendToTerminal('🤖 Interactive ASM Dashboard loaded\n');
        appendToTerminal('💡 Start by entering a target domain or IP\n');
    </script>
</body>
</html>