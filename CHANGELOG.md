# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-07-11

### Added

-   **Interactive Agent Core:** Implemented a fully interactive, conversational agent using Node.js, replacing the previous one-shot script.
-   **Command Execution Loop:** Created the core feedback loop where the AI can propose commands, have them executed upon user approval, and analyze the output to inform its next action.
-   **Automated Scan Reporting:** Added a feature to automatically save the `stdout` and `stderr` of every executed command to a uniquely named log file in the `scan_reports/` directory.
-   **Centralized Error Logging:** Implemented a robust error logging system that captures command execution and API errors into a single `logs/error.log` file for easier debugging and fine-tuning.
-   **Environment Variable Management:** Integrated the `dotenv` package to manage the `GEMINI_API_KEY` securely via a `.env` file.
-   **`.gitignore`:** Created a `.gitignore` file to exclude sensitive files and unnecessary directories (`.env`, `node_modules`, `scan_reports`, `logs`) from version control.
-   **Tool Configuration Files:**
    -   Added a `config.ini` for `amass` to manage API keys for various data sources.
    -   Added a `provider-config.yaml` for `subfinder` to manage its API keys.
-   **Tool Verification & Setup:**
    -   Added a check to verify that `nmap`, `nuclei`, and `subfinder` are installed.
    -   Added a command to automatically update `nuclei` templates to the latest version.
-   **Strategic System Prompt:** Engineered a new, detailed system prompt to guide the AI's behavior, including:
    -   A clear mission objective and operational procedure.
    -   Explicit rules for using tool configuration files (`amass`, `subfinder`).
    -   Instructions for error handling and strategic pivoting.
    -   A defined structure for the final report.

### Changed

-   **Project Entry Point:** Shifted the primary execution from the old `cli.js` to the new `interactive.js`.
-   **`package.json`:**
    -   Removed the obsolete `bin` and `main` entries.
    -   Added a `start` script (`npm start`) for easy execution of the agent.
    -   Added `dotenv` as a project dependency.
-   **System Prompt:** Replaced the simple, static prompt with a dynamic, strategic one embedded directly in the `interactive.js` script for better performance and easier management.

### Removed

-   **Obsolete `cli.js`:** Deleted the old, non-interactive script.
-   **Obsolete Prompt Templates:** Removed the entire `prompt-templates/` directory and its `.md` files, as they are no longer needed for the new conversational workflow.
