#!/bin/bash

# Enhanced ASM Report Generator
# Usage: ./generate_report.sh [log_file] [format]
# Formats: html, pdf, markdown, json, all (default: all)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
LOG_FILE=""
FORMAT="all"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[+]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[-]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Enhanced ASM Report Generator"
    echo ""
    echo "Usage: $0 [log_file] [format]"
    echo ""
    echo "Arguments:"
    echo "  log_file    Path to the ASM log file (optional - will auto-detect latest)"
    echo "  format      Output format: html, pdf, markdown, json, all (default: all)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Generate all formats from latest log"
    echo "  $0 scan_reports/my_report.log        # Generate all formats from specific log"
    echo "  $0 scan_reports/my_report.log html   # Generate only HTML format"
    echo ""
    echo "Available formats:"
    echo "  html      - Interactive HTML report with modern styling"
    echo "  pdf       - Professional PDF report for sharing"
    echo "  markdown  - Markdown format for documentation"
    echo "  json      - Structured JSON data for integration"
    echo "  all       - Generate all formats (default)"
}

# Function to find the latest ASM report log
find_latest_log() {
    local latest_log=$(find "$SCRIPT_DIR/scan_reports" -name "*Attack_Surface_Management_Report*.log" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    
    if [[ -z "$latest_log" ]]; then
        print_error "No ASM report logs found in scan_reports directory"
        exit 1
    fi
    
    echo "$latest_log"
}

# Function to validate log file
validate_log_file() {
    local log_file="$1"
    
    if [[ ! -f "$log_file" ]]; then
        print_error "Log file not found: $log_file"
        exit 1
    fi
    
    if ! grep -q "Attack Surface Management Report" "$log_file"; then
        print_warning "Log file doesn't appear to be an ASM report"
        print_info "Continuing anyway..."
    fi
}

# Function to generate reports
generate_reports() {
    local log_file="$1"
    local format="$2"
    
    print_info "Log file: $log_file"
    print_info "Format: $format"
    print_info "Output directory: $SCRIPT_DIR/scan_reports"
    echo ""
    
    # Update the log file path in the generator scripts
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s|const reportLogPath = '.*';|const reportLogPath = '$log_file';|" "$SCRIPT_DIR/generate_multi_format_report.js"
        sed -i '' "s|const reportLogPath = '.*';|const reportLogPath = '$log_file';|" "$SCRIPT_DIR/generate_html_report.js"
    else
        # Linux
        sed -i "s|const reportLogPath = '.*';|const reportLogPath = '$log_file';|" "$SCRIPT_DIR/generate_multi_format_report.js"
        sed -i "s|const reportLogPath = '.*';|const reportLogPath = '$log_file';|" "$SCRIPT_DIR/generate_html_report.js"
    fi
    
    case "$format" in
        "html")
            print_status "Generating HTML report..."
            node "$SCRIPT_DIR/generate_html_report.js"
            ;;
        "all")
            print_status "Generating all report formats..."
            node "$SCRIPT_DIR/generate_multi_format_report.js"
            ;;
        *)
            print_error "Unsupported format: $format"
            print_info "Supported formats: html, pdf, markdown, json, all"
            exit 1
            ;;
    esac
    
    echo ""
    print_status "Report generation completed!"
    
    # List generated files
    print_info "Generated files:"
    ls -la "$SCRIPT_DIR/scan_reports/enhanced_asm_report"* 2>/dev/null | while read line; do
        echo "  $line"
    done
}

# Main script logic
main() {
    # Parse arguments
    if [[ $# -eq 0 ]]; then
        # No arguments - use latest log and all formats
        LOG_FILE=$(find_latest_log)
        FORMAT="all"
    elif [[ $# -eq 1 ]]; then
        if [[ "$1" == "-h" || "$1" == "--help" ]]; then
            show_usage
            exit 0
        elif [[ "$1" =~ ^(html|pdf|markdown|json|all)$ ]]; then
            # First argument is format
            LOG_FILE=$(find_latest_log)
            FORMAT="$1"
        else
            # First argument is log file
            LOG_FILE="$1"
            FORMAT="all"
        fi
    elif [[ $# -eq 2 ]]; then
        LOG_FILE="$1"
        FORMAT="$2"
    else
        print_error "Too many arguments"
        show_usage
        exit 1
    fi
    
    # Validate inputs
    validate_log_file "$LOG_FILE"
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed or not in PATH"
        exit 1
    fi
    
    # Check if required npm packages are installed
    if [[ ! -d "$SCRIPT_DIR/node_modules" ]]; then
        print_warning "Node modules not found. Installing dependencies..."
        cd "$SCRIPT_DIR"
        npm install
    fi
    
    # Generate reports
    generate_reports "$LOG_FILE" "$FORMAT"
    
    # Open HTML report if generated
    if [[ "$FORMAT" == "html" || "$FORMAT" == "all" ]]; then
        local html_file="$SCRIPT_DIR/scan_reports/enhanced_asm_report.html"
        if [[ -f "$html_file" ]]; then
            print_info "Opening HTML report in browser..."
            if command -v xdg-open &> /dev/null; then
                xdg-open "$html_file" &
            elif command -v open &> /dev/null; then
                open "$html_file" &
            else
                print_info "HTML report available at: file://$html_file"
            fi
        fi
    fi
}

# Run main function
main "$@"
