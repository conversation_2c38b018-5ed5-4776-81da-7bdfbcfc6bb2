require('dotenv').config();

const readline = require('readline');
const { spawn } = require('child_process');
const { GoogleGenerativeAI } = require('@google/generative-ai');

const fs = require('fs');
const path = require('path');

// --- SETUP ---
// 1. Create necessary directories
const reportsDir = path.join(__dirname, 'scan_reports');
const logsDir = path.join(__dirname, 'logs');
[reportsDir, logsDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
    console.log(`[+] Created directory: ${dir}`);
  }
});

// 2. Setup Error Logging
const errorLogPath = path.join(logsDir, 'error.log');
function logError(errorMessage) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ERROR: ${errorMessage}\n`;
  fs.appendFile(errorLogPath, logMessage, (err) => {
    if (err) {
      console.error("FATAL: Could not write to error log file.", err);
    }
  });
}

// 3. Get API Key
const apiKey = process.env.GEMINI_API_KEY;
if (!apiKey) {
  const err = "[ERROR] GEMINI_API_KEY environment variable not set.";
  console.error(`\n${err}`);
  logError(err);
  process.exit(1);
}
const genAI = new GoogleGenerativeAI(apiKey);
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

// 4. Setup readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: 'gemini-black> '
});

// 4. Define the AI's core persona for the chat
const initialSystemPrompt = 'You are Gemini Black, an elite AI penetration testing assistant. Your operational directive is to conduct a comprehensive Attack Surface Management (ASM) assessment with precision and stealth.\n\n**Primary Objective:**\nYour ultimate goal is to map the target\'s entire digital footprint, identify potential vulnerabilities, and provide a clear, actionable report. You must think multiple steps ahead.\n\n**Standard Operating Procedure (SOP):**\n1.  **Analyze:** I will provide an initial objective.\n2.  **Command:** You will determine the best single command to execute on a Parrot OS system to advance the mission. You MUST wrap this command in a ```bash code block.\n3.  **Execute:** I will execute the command and return the complete STDOUT and STDERR to you.\n4.  **Analyze & Repeat:** You will analyze the output, correlate it with previous findings, and determine the next logical command.\n\n**Rules of Engagement:**\n*   **One Command at a Time:** Propose a single, focused command per turn.\n*   **Background Commands:** For long-running reconnaissance tools like `amass` or `subfinder`, you **MUST** append ` &` to the command to run it in the background. When a command is run in the background, I will immediately inform you that it has been launched, and you should then propose the next logical step or ask for further instructions, rather than waiting for its output.\n*   **Tool Configuration:**\n    *   When using \'amass\', you **MUST** use the flag: `-config /home/<USER>/gemini-black/config.ini`.\n    *   When using \'subfinder\', you **MUST** use the flag: `-provider-config /home/<USER>/gemini-black/provider-config.yaml`.\n*   **Error Handling:** If a command fails, analyze the error message. Attempt to reformulate the command or select an alternative tool to achieve the objective.\n*   **Methodology:** Follow a logical ASM methodology. Start with broad, passive reconnaissance (amass, subfinder) before moving to more active scanning (nmap, nuclei).\n*   **Reporting:** When you have gathered sufficient information, state that you are concluding the assessment and provide your final report in a structured Markdown format with sections for "Executive Summary," "Subdomains Discovered," "Open Ports," and "Potential Vulnerabilities."\n*   **Conciseness:** Do not provide explanations unless the output is ambiguous or you are... [truncated]';

// 5. Initialize chat history and session target
let chatHistory = [];
let sessionTarget = '';

function initializeChat(target) {
  sessionTarget = target.replace(/[^a-zA-Z0-9.-]/g, '_'); // Sanitize the target for use in filenames
  chatHistory = [
    { role: 'user', parts: [{ text: initialSystemPrompt }] },
    { role: 'model', parts: [{ text: `Understood. I am ready to assist. My target is ${target}. What is our first objective?` }] }
  ];
  console.log(`[+] New session initialized for target: ${target}`);
}

// --- CORE FUNCTIONS ---

// Function to parse the command from the AI's text
function parseCommand(text) {
  const match = text.match(/```bash\n([\s\S]*?)\n```/);
  return match ? match[1].trim() : null;
}

function executeCommand(command, target, callback) {
  console.log(`\n[+] Executing: ${command}`);
  let stdout = '';
  let stderr = '';

  // Using spawn is more robust for long-running processes and streaming I/O
  const parts = command.split(' ');
  const tool = parts[0];
  const args = parts.slice(1);

  const child = spawn(tool, args);

  child.stdout.on('data', (data) => {
    const output = data.toString();
    process.stdout.write(output);
    stdout += output;
  });

  child.stderr.on('data', (data) => {
    const output = data.toString();
    process.stderr.write(output);
    stderr += output;
  });

  child.on('close', (code) => {
    console.log(`\n[+] Command finished with code ${code}.`);

    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const commandSanitized = command.replace(/[^a-zA-Z0-9_.-]/g, '_').substring(0, 50);
    const filename = `${timestamp}_${target}_${commandSanitized}.log`;
    const filepath = path.join(reportsDir, filename);
    const fileContent = `Command: ${command}\nExit Code: ${code}\n\n--- STDOUT ---\n${stdout}\n--- STDERR ---\n${stderr}`;

    fs.writeFile(filepath, fileContent, (err) => {
      if (err) {
        const writeErr = `Failed to save report file: ${err.message}`;
        console.error(`\n[-] ${writeErr}`);
        logError(writeErr);
      } else {
        console.log(`[+] Report saved to: ${filepath}`);
      }
    });

    callback(null, { code, stdout, stderr });
  });

  child.on('error', (err) => {
    const execErr = `Failed to start command: ${err.message}`;
    console.error(`\n[-] ${execErr}`);
    logError(`Command: \"${command}\" | ${execErr}`);
    callback(err, null);
  });
}

// --- MAIN CHAT LOOP ---

console.log('--- Gemini-Black Interactive Session ---');
console.log('First, please enter the primary target (e.g., example.com):');
rl.prompt();

let isInitialized = false;

async function chatLoop(userInput) {
  try {
    chatHistory.push({ role: 'user', parts: [{ text: userInput }] });
    const chat = model.startChat({ history: chatHistory });
    const result = await chat.sendMessage(userInput);
    const response = await result.response;
    const aiText = response.text();

    chatHistory.push({ role: 'model', parts: [{ text: aiText }] });

    // Check for an empty response from the AI
    if (!aiText || aiText.trim() === '') {
      console.log('\n🤖 AI: (Received empty response. Re-prompting...)');
      const retryPrompt = 'The last response was empty. Please analyze the previous tool output and provide the next command or a final summary.';
      chatLoop(retryPrompt); // Continue the loop with the retry prompt
      return;
    }

    console.log(`\n🤖 AI: ${aiText}\n`);

    const commandToExecute = parseCommand(aiText);

    if (commandToExecute) {
      rl.question(`> Execute this command? (y/n) `, (answer) => {
        if (answer.toLowerCase() === 'y') {
          // Check if the command is meant to run in the background
          if (commandToExecute.endsWith(' &')) {
            console.log(`[+] Command "${commandToExecute.trim()}" launched in background.`);
            const toolFeedback = `Command "${commandToExecute.trim()}" launched in background. Please propose the next logical step or ask for further instructions.`;
            chatLoop(toolFeedback); // Immediately send feedback to AI and continue
          } else {
            executeCommand(commandToExecute, sessionTarget, (err, result) => {
              if (err) {
                const toolFeedback = `Execution Error: ${err.message}`;
                chatLoop(toolFeedback);
                return;
              }
              const toolFeedback = `Command executed. Here is the output:\n\n---\nExit Code: ${result.code}\nSTDOUT:\n${result.stdout}\nSTDERR:\n${result.stderr}\n---\nNow, what is the next command?`;
              chatLoop(toolFeedback);
            });
          }
        } else {
          console.log('> Execution cancelled.');
          rl.prompt();
        }
      });
    } else {
      // If the AI gives a summary or asks a question, wait for the user's next input
      rl.prompt();
    }
  } catch (error) {
    const apiErr = `[API ERROR] ${error.message}`;
    console.error(`\n${apiErr}`);
    logError(apiErr);
    rl.prompt();
  }
}

// Start the conversation loop when the user provides the first line of input
rl.on('line', async (line) => {
  const trimmedLine = line.trim();
  if (trimmedLine.toLowerCase() === 'exit') {
    rl.close();
  } else if (!isInitialized) {
    initializeChat(trimmedLine);
    isInitialized = true;
    console.log(`\nNow, what is the first objective for scanning ${trimmedLine}?`);
    rl.prompt();
  } else {
    chatLoop(trimmedLine);
  }
}).on('close', () => {
  console.log('\nSession closed. Goodbye!');
  process.exit(0);
});