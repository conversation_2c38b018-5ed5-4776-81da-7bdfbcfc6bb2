{"name": "gemini-black", "version": "1.0.0", "description": "An autonomous ASM agent powered by Google Gemini.", "scripts": {"start": "node interactive.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["asm", "pentest", "gemini", "cli"], "author": "s4ng", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "docxtemplater": "^3.65.2", "dotenv": "^17.2.0", "html-pdf-node": "^1.0.8", "jspdf": "^3.0.1", "pizzip": "^3.2.0", "puppeteer": "^24.12.1", "yargs": "^17.7.2"}}