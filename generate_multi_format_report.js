const fs = require('fs');
const path = require('path');
const htmlPdf = require('html-pdf-node');

// Define paths
const reportLogPath = '/home/<USER>/gemini-black/scan_reports/2025-07-11T16-47-47.283Z_tngdigital.com.my_echo____Attack_Surface_Management_Report__tngdigit.log';
const outputDir = '/home/<USER>/gemini-black/scan_reports';

async function generateMultiFormatReport() {
    try {
        console.log('[+] Starting multi-format report generation...');
        
        // Read and parse the log content
        const logContent = fs.readFileSync(reportLogPath, 'utf8');
        const parsedData = parseReportData(logContent);
        
        // Generate different formats
        await generateHtmlReport(parsedData);
        await generatePdfReport(parsedData);
        await generateMarkdownReport(parsedData);
        await generateJsonReport(parsedData);
        
        console.log('[+] All report formats generated successfully!');
        console.log(`[+] Reports saved in: ${outputDir}`);
        
    } catch (error) {
        console.error(`[-] Error generating reports: ${error.message}`);
    }
}

function parseReportData(content) {
    const sections = {};
    const lines = content.split('\n');
    let currentSection = '';
    let currentContent = [];

    for (const line of lines) {
        if (line.startsWith('## ')) {
            if (currentSection) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = line.substring(3).trim().toLowerCase().replace(/ /g, '_');
            currentContent = [];
        } else if (currentSection) {
            currentContent.push(line);
        }
    }
    if (currentSection) {
        sections[currentSection] = currentContent.join('\n').trim();
    }

    // Extract metadata
    const firstLineMatch = content.match(/^#\s*(.*?:\s*)(.*)/);
    const reportTitle = firstLineMatch ? firstLineMatch[0].trim() : 'ASM Report';
    const targetDomain = firstLineMatch ? firstLineMatch[2].trim() : 'unknown.com';

    // Calculate metrics
    const subdomainCount = (sections.subdomains_discovered || '').split('\n').filter(line => line.trim().startsWith('*')).length;
    const vulnerabilityCount = (sections.potential_vulnerabilities || '').split(/^\d+\./).length - 1;
    const openPortsCount = (sections.open_ports || '').split('**Port').length - 1;
    
    // Risk assessment
    const riskLevel = vulnerabilityCount > 3 ? 'HIGH' : vulnerabilityCount > 1 ? 'MEDIUM' : 'LOW';
    const riskColor = riskLevel === 'HIGH' ? '#dc3545' : riskLevel === 'MEDIUM' ? '#fd7e14' : '#28a745';

    return {
        reportTitle,
        targetDomain,
        date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
        time: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        timestamp: new Date().toISOString(),
        subdomainCount,
        vulnerabilityCount,
        openPortsCount,
        riskLevel,
        riskColor,
        sections,
        executiveSummary: sections.executive_summary || 'No executive summary available.',
        subdomainsDiscovered: sections.subdomains_discovered || 'No subdomains discovered.',
        openPorts: sections.open_ports || 'No open ports identified.',
        potentialVulnerabilities: sections.potential_vulnerabilities || 'No vulnerabilities identified.',
        recommendations: sections.recommendations || 'No specific recommendations available.'
    };
}

async function generateHtmlReport(data) {
    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.reportTitle}</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; line-height: 1.6; color: #333; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; text-align: center; border-radius: 10px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .meta-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .meta-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .meta-card h3 { color: #666; font-size: 0.9em; text-transform: uppercase; margin-bottom: 10px; }
        .meta-card .value { font-size: 2em; font-weight: bold; color: #333; }
        .risk-level { color: ${data.riskColor} !important; }
        .section { background: white; margin-bottom: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }
        .section-header { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }
        .section-header h2 { color: #495057; font-size: 1.5em; margin: 0; }
        .section-content { padding: 20px; }
        .vulnerability-item { background: #fff5f5; border-left: 4px solid #dc3545; padding: 15px; margin-bottom: 15px; border-radius: 0 4px 4px 0; }
        .port-item { background: #f0f8ff; border-left: 4px solid #007bff; padding: 10px; margin-bottom: 10px; border-radius: 0 4px 4px 0; }
        .subdomain-list { columns: 2; column-gap: 20px; }
        .subdomain-item { break-inside: avoid; padding: 5px 0; border-bottom: 1px solid #eee; }
        .recommendation { background: #f8f9fa; border-left: 4px solid #28a745; padding: 15px; margin-bottom: 15px; border-radius: 0 4px 4px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TECHLAB SECURITY</h1>
            <div style="font-size: 1.2em; opacity: 0.9;">CYBER THREAT INTELLIGENCE</div>
            <h2 style="margin-top: 20px;">ASM Report</h2>
            <div style="font-size: 1.2em; opacity: 0.9;">${data.targetDomain}</div>
            <div style="margin-top: 10px;">Date: ${data.date} ${data.time}</div>
        </div>
        
        <div class="meta-info">
            <div class="meta-card">
                <h3>Subdomains Found</h3>
                <div class="value">${data.subdomainCount}</div>
            </div>
            <div class="meta-card">
                <h3>Open Ports</h3>
                <div class="value">${data.openPortsCount}</div>
            </div>
            <div class="meta-card">
                <h3>Vulnerabilities</h3>
                <div class="value">${data.vulnerabilityCount}</div>
            </div>
            <div class="meta-card">
                <h3>Risk Level</h3>
                <div class="value risk-level">${data.riskLevel}</div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header"><h2>Executive Summary</h2></div>
            <div class="section-content"><p>${data.executiveSummary.replace(/\n/g, '</p><p>')}</p></div>
        </div>
        
        <div class="section">
            <div class="section-header"><h2>Subdomains Discovered</h2></div>
            <div class="section-content">
                <div class="subdomain-list">
                    ${data.subdomainsDiscovered.split('\n')
                        .filter(line => line.trim().startsWith('*'))
                        .map(line => `<div class="subdomain-item">${line.trim()}</div>`)
                        .join('')}
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-header"><h2>Open Ports</h2></div>
            <div class="section-content">
                ${data.openPorts.split('\n')
                    .filter(line => line.includes('**Port'))
                    .map(line => `<div class="port-item">${line.replace(/\*\*/g, '')}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header"><h2>Potential Vulnerabilities</h2></div>
            <div class="section-content">
                ${data.potentialVulnerabilities.split(/^\d+\./).slice(1)
                    .map(vuln => `<div class="vulnerability-item">${vuln.trim()}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header"><h2>Recommendations</h2></div>
            <div class="section-content">
                ${data.recommendations.split(/^\d+\./).slice(1)
                    .map(rec => `<div class="recommendation">${rec.trim()}</div>`)
                    .join('')}
            </div>
        </div>
        
        <div class="footer">
            <p>This report was generated automatically by the ASM Scanner v2.0</p>
            <p>© 2025 TechLab Security - Confidential</p>
        </div>
    </div>
</body>
</html>`;

    const htmlPath = path.join(outputDir, 'enhanced_asm_report.html');
    fs.writeFileSync(htmlPath, htmlContent);
    console.log(`[+] HTML report generated: ${htmlPath}`);
}

async function generatePdfReport(data) {
    try {
        const htmlContent = generatePdfHtmlContent(data);
        const options = {
            format: 'A4',
            margin: { top: '20mm', bottom: '20mm', left: '15mm', right: '15mm' },
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: '<div style="font-size:10px; text-align:center; width:100%;">ASM Report - Confidential</div>',
            footerTemplate: '<div style="font-size:10px; text-align:center; width:100%;"><span class="pageNumber"></span> of <span class="totalPages"></span></div>'
        };

        const file = { content: htmlContent };
        const pdfBuffer = await htmlPdf.generatePdf(file, options);
        
        const pdfPath = path.join(outputDir, 'enhanced_asm_report.pdf');
        fs.writeFileSync(pdfPath, pdfBuffer);
        console.log(`[+] PDF report generated: ${pdfPath}`);
    } catch (error) {
        console.log(`[-] PDF generation failed: ${error.message}`);
    }
}

function generatePdfHtmlContent(data) {
    return `<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>${data.reportTitle}</title>
<style>
body { font-family: Arial, sans-serif; line-height: 1.4; color: #333; font-size: 12px; margin: 0; }
.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; margin-bottom: 20px; }
.header h1 { font-size: 24px; margin-bottom: 5px; }
.meta-info { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
.meta-card { background: #f8f9fa; padding: 15px; text-align: center; border: 1px solid #dee2e6; }
.meta-card h3 { color: #666; font-size: 10px; text-transform: uppercase; margin-bottom: 5px; }
.meta-card .value { font-size: 18px; font-weight: bold; color: #333; }
.risk-level { color: ${data.riskColor} !important; }
.section { margin-bottom: 20px; page-break-inside: avoid; }
.section-header { background: #f8f9fa; padding: 10px; border-bottom: 2px solid #dee2e6; }
.section-header h2 { color: #495057; font-size: 16px; margin: 0; }
.section-content { padding: 15px; border: 1px solid #dee2e6; border-top: none; }
.page-break { page-break-before: always; }
</style></head><body>
<div class="header">
    <h1>TECHLAB SECURITY</h1>
    <div>CYBER THREAT INTELLIGENCE</div>
    <h2 style="margin-top: 15px;">ASM Report</h2>
    <div>${data.targetDomain}</div>
    <div style="margin-top: 8px;">Date: ${data.date} ${data.time}</div>
</div>
<div class="meta-info">
    <div class="meta-card"><h3>Subdomains</h3><div class="value">${data.subdomainCount}</div></div>
    <div class="meta-card"><h3>Open Ports</h3><div class="value">${data.openPortsCount}</div></div>
    <div class="meta-card"><h3>Vulnerabilities</h3><div class="value">${data.vulnerabilityCount}</div></div>
    <div class="meta-card"><h3>Risk Level</h3><div class="value risk-level">${data.riskLevel}</div></div>
</div>
<div class="section">
    <div class="section-header"><h2>Executive Summary</h2></div>
    <div class="section-content">${data.executiveSummary}</div>
</div>
<div class="section page-break">
    <div class="section-header"><h2>Findings Summary</h2></div>
    <div class="section-content">
        <p><strong>Subdomains:</strong> ${data.subdomainCount} discovered</p>
        <p><strong>Open Ports:</strong> ${data.openPortsCount} identified</p>
        <p><strong>Vulnerabilities:</strong> ${data.vulnerabilityCount} potential issues</p>
        <p><strong>Risk Assessment:</strong> ${data.riskLevel}</p>
    </div>
</div>
</body></html>`;
}

async function generateMarkdownReport(data) {
    const markdownContent = `# ${data.reportTitle}

**Target:** ${data.targetDomain}  
**Date:** ${data.date} ${data.time}  
**Risk Level:** ${data.riskLevel}

## Summary Metrics

| Metric | Count |
|--------|-------|
| Subdomains Discovered | ${data.subdomainCount} |
| Open Ports | ${data.openPortsCount} |
| Potential Vulnerabilities | ${data.vulnerabilityCount} |
| Risk Level | ${data.riskLevel} |

## Executive Summary

${data.executiveSummary}

## Subdomains Discovered

${data.subdomainsDiscovered}

## Open Ports

${data.openPorts}

## Potential Vulnerabilities

${data.potentialVulnerabilities}

## Recommendations

${data.recommendations}

---
*Report generated automatically by ASM Scanner v2.0*  
*© 2025 TechLab Security - Confidential*
`;

    const markdownPath = path.join(outputDir, 'enhanced_asm_report.md');
    fs.writeFileSync(markdownPath, markdownContent);
    console.log(`[+] Markdown report generated: ${markdownPath}`);
}

async function generateJsonReport(data) {
    const jsonData = {
        metadata: {
            reportTitle: data.reportTitle,
            targetDomain: data.targetDomain,
            generatedAt: data.timestamp,
            reportVersion: '2.0',
            scanner: 'ASM Scanner'
        },
        metrics: {
            subdomainCount: data.subdomainCount,
            openPortsCount: data.openPortsCount,
            vulnerabilityCount: data.vulnerabilityCount,
            riskLevel: data.riskLevel
        },
        findings: {
            executiveSummary: data.executiveSummary,
            subdomainsDiscovered: data.subdomainsDiscovered.split('\n').filter(line => line.trim().startsWith('*')).map(line => line.trim().substring(1).trim()),
            openPorts: data.openPorts.split('\n').filter(line => line.includes('**Port')),
            vulnerabilities: data.potentialVulnerabilities.split(/^\d+\./).slice(1).map(v => v.trim()),
            recommendations: data.recommendations.split(/^\d+\./).slice(1).map(r => r.trim())
        }
    };

    const jsonPath = path.join(outputDir, 'enhanced_asm_report.json');
    fs.writeFileSync(jsonPath, JSON.stringify(jsonData, null, 2));
    console.log(`[+] JSON report generated: ${jsonPath}`);
}

generateMultiFormatReport();
