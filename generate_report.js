const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const fs = require('fs');
const path = require('path');

// Define paths
const reportLogPath = '/home/<USER>/gemini-black/scan_reports/2025-07-11T16-47-47.283Z_tngdigital.com.my_echo____Attack_Surface_Management_Report__tngdigit.log';
const templatePath = '/home/<USER>/gemini-black/report template/template report.docx';
const outputPath = '/home/<USER>/gemini-black/scan_reports/final_scan_report.docx';

async function generateDocxReport() {
    try {
        // 1. Read the content of the scan report log file
        const logContent = fs.readFileSync(reportLogPath, 'utf8');

        // 2. Parse the log content to extract relevant sections
        const parseReport = (content) => {
            const sections = {};
            const lines = content.split('\n');
            let currentSection = '';
            let currentContent = [];

            for (const line of lines) {
                if (line.startsWith('## ')) {
                    if (currentSection) {
                        sections[currentSection] = currentContent.join('\n').trim();
                    }
                    currentSection = line.substring(3).trim().toLowerCase().replace(/ /g, '_');
                    currentContent = [];
                } else if (currentSection) {
                    currentContent.push(line);
                }
            }
            if (currentSection) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            return sections;
        };

        const parsedSections = parseReport(logContent);

        // Extract report title and target domain from the first line
        const firstLineMatch = logContent.match(/^#\s*(.*?:\s*)(.*)/);
        const reportTitle = firstLineMatch ? firstLineMatch[0].trim() : 'ASM Report';
        const targetDomain = firstLineMatch ? firstLineMatch[2].trim() : 'unknown.com';

        const data = {
            report_title: reportTitle,
            target_domain: targetDomain,
            executive_summary: parsedSections.executive_summary || '',
            subdomains_discovered: parsedSections.subdomains_discovered || '',
            open_ports: parsedSections.open_ports || '',
            potential_vulnerabilities: parsedSections.potential_vulnerabilities || '',
            recommendations: parsedSections.recommendations || '',
            date: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })
        };

        // 3. Load the .docx template
        const content = fs.readFileSync(templatePath, 'binary');
        const zip = new PizZip(content);
        const doc = new Docxtemplater(zip, {
            paragraphLoop: true,
            linebreaks: true,
        });

        // 4. Set the data for the template
        doc.setData(data);

        // 5. Render the document (replace all placeholders)
        doc.render();

        // 6. Generate the output .docx file
        const buf = doc.getZip().generate({
            type: 'nodebuffer',
            compression: 'DEFLATE',
        });

        // Save the generated .docx file
        fs.writeFileSync(outputPath, buf);
        console.log(`[+] DOCX report generated successfully at: ${outputPath}`);

    } catch (error) {
        console.error(`[-] Error generating DOCX report: ${error.message}`);
        if (error.properties && error.properties.errors) {
            console.error('Template errors:', error.properties.errors);
        }
    }
}

generateDocxReport();
