# 🤖 Interactive AI Attack Surface Management System

A next-generation ASM platform powered by Google Gemini AI with real-time intelligence, adaptive scanning, and interactive web dashboard.

## 🚀 Quick Start

### 1. Web Dashboard (Recommended)
```bash
./start_interactive_asm.sh web
```
Access: http://localhost:3000

### 2. Enhanced CLI
```bash
./start_interactive_asm.sh cli
```

### 3. Auto-detect Best Mode
```bash
./start_interactive_asm.sh
```

## 🎯 Key Features

### 🧠 **AI Intelligence**
- **Autonomous Decision Making** - AI determines optimal scan strategies
- **Real-time Risk Assessment** - Continuous risk evaluation during scans
- **Intelligent Correlation** - Connects findings across multiple tools
- **Adaptive Scanning** - Modifies approach based on discovered assets
- **Strategic Planning** - Multi-step reconnaissance strategies

### 🌐 **Interactive Web Dashboard**
- **Real-time Monitoring** - Live scan progress and results
- **AI Chat Interface** - Natural language interaction with AI
- **Visual Metrics** - Dynamic charts and risk indicators
- **Multi-session Management** - Handle multiple targets simultaneously
- **Live Terminal Output** - Real-time command execution feedback

### 🔍 **Enhanced Scanning Capabilities**
- **Passive Reconnaissance** - OSINT, subdomain enumeration, DNS analysis
- **Active Assessment** - Port scanning, service detection, vulnerability testing
- **Continuous Monitoring** - Ongoing change detection and alerting
- **Emergency Response** - Rapid assessment for incident response

### 📊 **Advanced Analytics**
- **Risk Scoring** - Automated risk level calculation
- **Trend Analysis** - Historical data comparison
- **Finding Correlation** - Cross-reference discoveries
- **Intelligence Reports** - AI-generated insights and recommendations

## 🛠 System Architecture

### Core Components

1. **Enhanced AI Engine** (`enhanced_ai_asm.js`)
   - Advanced AI system with strategic thinking
   - Real-time analysis and decision making
   - Intelligent tool selection and sequencing

2. **Web Dashboard** (`web_dashboard.js`)
   - Interactive web interface
   - Real-time WebSocket communication
   - Multi-session management
   - Live command execution

3. **Classic CLI** (`interactive.js`)
   - Original command-line interface
   - Basic AI integration
   - Manual command approval

4. **Report Generation** (Multiple formats)
   - HTML, PDF, Markdown, JSON
   - Professional templates
   - Automated risk assessment

### Technology Stack
- **AI**: Google Gemini 2.5 Flash
- **Backend**: Node.js, Express, Socket.IO
- **Frontend**: HTML5, CSS3, JavaScript
- **Tools**: Nmap, Subfinder, Nuclei, Amass

## 📋 Usage Modes

### 🌐 Web Dashboard Mode
**Best for**: Interactive analysis, presentations, team collaboration

**Features**:
- Real-time scan monitoring
- AI chat interface
- Visual metrics dashboard
- Multi-session support
- Live terminal output

**Usage**:
```bash
./start_interactive_asm.sh web --port 3000
```

### 💻 Enhanced CLI Mode
**Best for**: Advanced users, automation, scripting

**Features**:
- Intelligent AI analysis
- Real-time risk assessment
- Adaptive scanning strategies
- Advanced correlation
- Strategic planning

**Usage**:
```bash
./start_interactive_asm.sh cli
```

### 🔧 Classic CLI Mode
**Best for**: Traditional workflow, manual control

**Features**:
- Basic AI integration
- Manual command approval
- Simple interface
- Compatible with original workflow

**Usage**:
```bash
./start_interactive_asm.sh classic
```

## 🎯 Scanning Strategies

### Passive Reconnaissance
- Subdomain enumeration (Subfinder, Amass)
- DNS analysis and zone transfers
- WHOIS and registration data
- Certificate transparency logs
- Social media and OSINT gathering

### Active Assessment
- Port scanning (Nmap)
- Service version detection
- Vulnerability scanning (Nuclei)
- Web application testing
- SSL/TLS configuration analysis

### Continuous Monitoring
- Periodic asset discovery
- Change detection and alerting
- New vulnerability identification
- Risk trend analysis
- Automated reporting

## 📊 AI Intelligence Features

### Strategic Decision Making
The AI system makes intelligent decisions about:
- **Tool Selection** - Best tools for each target type
- **Scan Sequencing** - Optimal order of operations
- **Risk Prioritization** - Focus on high-risk findings first
- **Resource Allocation** - Efficient use of scanning resources

### Real-time Analysis
- **Pattern Recognition** - Identify attack patterns and trends
- **Anomaly Detection** - Spot unusual configurations or behaviors
- **Correlation Analysis** - Connect findings across different scans
- **Risk Assessment** - Continuous risk level evaluation

### Adaptive Behavior
- **Learning from Results** - Improve strategies based on findings
- **Context Awareness** - Adjust approach based on target characteristics
- **Dynamic Prioritization** - Re-prioritize based on new discoveries
- **Intelligent Recommendations** - Suggest next steps and remediation

## 🔧 Configuration

### Environment Setup
```bash
# Required environment variables
GEMINI_API_KEY=your_gemini_api_key_here
PORT=3000  # Optional, for web dashboard

# Optional tool configurations
SUBFINDER_CONFIG=/path/to/provider-config.yaml
AMASS_CONFIG=/path/to/config.ini
```

### Tool Configuration
- **Subfinder**: Configure API keys in `provider-config.yaml`
- **Amass**: Set up data sources in `config.ini`
- **Nuclei**: Templates auto-updated on startup

## 📈 Dashboard Features

### Real-time Metrics
- **Subdomains Discovered** - Live count with details
- **Open Ports** - Service identification and risk assessment
- **Vulnerabilities** - Severity classification and CVSS scores
- **Risk Level** - Overall security posture assessment

### Interactive Elements
- **AI Chat** - Natural language queries and commands
- **Live Terminal** - Real-time command output
- **Quick Actions** - One-click scan initiation
- **Session Management** - Multiple concurrent assessments

### Visual Indicators
- **Risk Color Coding** - Red (High), Orange (Medium), Green (Low)
- **Status Indicators** - Running, Completed, Error states
- **Progress Tracking** - Scan completion percentage
- **Timeline View** - Historical scan data

## 🚨 Risk Assessment

### Automated Risk Scoring
The system calculates risk levels based on:
- **Vulnerability Count** - Number and severity of findings
- **Exposure Level** - Public accessibility of services
- **Service Criticality** - Importance of discovered services
- **Configuration Issues** - Misconfigurations and weaknesses

### Risk Levels
- **CRITICAL** - Immediate attention required
- **HIGH** - Significant security concerns
- **MEDIUM** - Moderate risk, should be addressed
- **LOW** - Minor issues, low priority
- **UNKNOWN** - Insufficient data for assessment

## 🔄 Workflow Examples

### Basic Domain Assessment
1. Start web dashboard: `./start_interactive_asm.sh web`
2. Enter target domain in dashboard
3. AI automatically creates scan strategy
4. Monitor real-time results
5. Review AI analysis and recommendations
6. Generate professional reports

### Advanced Penetration Testing
1. Use enhanced CLI: `./start_interactive_asm.sh cli`
2. Specify target and assessment type
3. AI guides through reconnaissance phases
4. Real-time correlation of findings
5. Adaptive scanning based on discoveries
6. Comprehensive risk assessment

### Continuous Monitoring Setup
1. Configure monitoring targets
2. Set up automated scanning schedules
3. Define alerting thresholds
4. Monitor dashboard for changes
5. Receive AI-generated insights
6. Track security posture over time

## 🛡️ Security Considerations

### Ethical Usage
- Only scan systems you own or have permission to test
- Respect rate limits and avoid aggressive scanning
- Follow responsible disclosure for vulnerabilities
- Comply with local laws and regulations

### Data Protection
- Scan results stored locally
- No data transmitted to external services (except AI API)
- Secure API key management
- Optional data encryption at rest

## 🔧 Troubleshooting

### Common Issues

**Web Dashboard Won't Start**
```bash
# Check if port is in use
lsof -i :3000

# Try different port
./start_interactive_asm.sh web --port 8080
```

**AI Not Responding**
```bash
# Check API key
echo $GEMINI_API_KEY

# Verify .env file
cat .env
```

**Tools Not Found**
```bash
# Install missing tools
sudo apt update
sudo apt install nmap
go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest
go install -v github.com/projectdiscovery/nuclei/v2/cmd/nuclei@latest
```

## 📞 Support & Updates

### System Maintenance
```bash
# Update system and tools
./start_interactive_asm.sh --update

# Clean old files
./start_interactive_asm.sh --cleanup

# Check system status
./start_interactive_asm.sh --status
```

### Getting Help
- Check logs in `logs/` directory
- Review scan reports in `scan_reports/`
- Use AI chat for troubleshooting guidance
- Consult tool documentation for specific issues

---

**Interactive AI ASM System v2.0**  
*Powered by Google Gemini AI*  
*© 2025 - Advanced Attack Surface Management*
